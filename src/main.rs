use std::convert::Infallible;
use strum::IntoEnumIterator;
use warp::{Filter, Rejection};
use crate::config::{Configuration, YamlConfiguration};
use crate::ethrpc::{ETH_RPC_NODE_URL, ETHERSCAN_CONFIG, EvmNetworkChainId};
use crate::redis::RedisPool;
use crate::redis_cluster::RedisClusterPool;
use crate::solrpc::{SOL_RPC_NODE_URL, SolNetworkId};

mod redis;
mod error;
mod handler;
mod ethrpc;
mod network;
mod config;
mod redis_cluster;
mod solrpc;

type Result<T> = std::result::Result<T, Rejection>;

fn with_redis(redis_pool: RedisPool) -> impl Filter<Extract = (RedisPool,), Error = Infallible> + Clone {
	warp::any().map(move || redis_pool.clone())
}
fn with_redis_cluster(redis_cluster_pool: RedisClusterPool) -> impl Filter<Extract = (RedisClusterPool,), Error = Infallible> + Clone {
	warp::any().map(move || redis_cluster_pool.clone())
}

#[tokio::main]
async fn main() {
	pretty_env_logger::init();
	let env = envy::from_env::<Configuration>().unwrap();
	let mut yaml_config: Option<YamlConfiguration> = None;
	if let Ok(f) = std::fs::File::open("/etc/rpc-proxy/config/config.yml") {
		yaml_config = serde_yaml::from_reader(f).expect("Could not read values.");
	}
	if let Ok(f) = std::fs::File::open("./config.yml") {
		yaml_config = serde_yaml::from_reader(f).expect("Could not read values.");
	}

	if let Some(yaml_config) = yaml_config {
		// Build local maps to minimize RwLock contention
		let mut eth_urls = std::collections::HashMap::new();
		let mut etherscan_configs = std::collections::HashMap::new();
		let mut sol_urls = std::collections::HashMap::new();

		for chain_network in yaml_config.network.iter() {
			if let Some(chain_id) = &chain_network.chain_id {
				let chain_id_u16 = *chain_id as u16;

				// Check EVM networks
				for evm_network_chain_id in EvmNetworkChainId::iter() {
					if evm_network_chain_id as u16 == chain_id_u16 {
						eth_urls.insert(evm_network_chain_id, chain_network.rpc.clone());
						if let Some(etherscan_config) = &chain_network.etherscan_config {
							etherscan_configs.insert(evm_network_chain_id, etherscan_config.clone());
						}
						break; // Found match, no need to continue
					}
				}

				// Check Solana networks
				for sol_network_chain_id in SolNetworkId::iter() {
					if sol_network_chain_id as u16 == chain_id_u16 {
						sol_urls.insert(sol_network_chain_id, chain_network.rpc.clone());
						break; // Found match, no need to continue
					}
				}
			}
		}

		// Single write operation for each global map
		{
			let mut eth_map = ETH_RPC_NODE_URL.write().unwrap();
			for (chain_id, urls) in eth_urls {
				eth_map.insert(chain_id, urls);
			}
		}

		{
			let mut etherscan_map = ETHERSCAN_CONFIG.write().unwrap();
			for (chain_id, config) in etherscan_configs {
				etherscan_map.insert(chain_id, config);
			}
		}

		{
			let mut sol_map = SOL_RPC_NODE_URL.write().unwrap();
			for (chain_id, urls) in sol_urls {
				sol_map.insert(chain_id, urls);
			}
		}
	}

	let redis_pool = redis::connect(env.redis_url.clone()).await.expect("can create mobc redis pool");
	let redis_cluster_pool = redis_cluster::connect(env.redis_url.clone()).await.expect("can create mobc redis pool");

	let log = warp::log("api");

	let health_route = warp::path!("health")
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::health_handler);
	let eth_rpc_route = warp::path!("eth")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::eth_rcp_handler);
	let bsc_rpc_route = warp::path!("bsc")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::bsc_rcp_handler);
	let polygon_rpc_route = warp::path!("polygon")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::polygon_rcp_handler);
	let avalanche_rpc_route = warp::path!("avalanche")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::avalanche_rcp_handler);
	let cronos_rpc_route = warp::path!("cronos")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::cronos_rcp_handler);
	let eth_goerli_rpc_route = warp::path!("eth_goerli")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::eth_goerli_rcp_handler);
	let bsc_testnet_rpc_route = warp::path!("bsc_testnet")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::bsc_testnet_rcp_handler);
	let polygon_mumbai_rpc_route = warp::path!("polygon_mumbai")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::polygon_mumbai_rcp_handler);
	let avalanche_fuji_rpc_route = warp::path!("avalanche_fuji")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::avalanche_fuji_rcp_handler);
	let cronos_testnet_rpc_route = warp::path!("cronos_testnet")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::cronos_testnet_rcp_handler);
	let sol_devnet_rpc_route = warp::path!("sol_devnet")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::sol_devnet_rcp_handler);
	let sol_mainnet_rpc_route = warp::path!("sol")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::sol_mainnet_rcp_handler);
	let op_mainnet_rpc_route = warp::path!("optimism")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::op_rcp_handler);
	let op_sepolia_rpc_route = warp::path!("optimism_sepolia")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::op_sepolia_rcp_handler);
	let arb_mainnet_rpc_route = warp::path!("arbitrum")
		.and(warp::post())
		.and(warp::body::content_length_limit(1024 * 16))
		.and(warp::body::json())
		.and(with_redis(redis_pool.clone()))
		.and(with_redis_cluster(redis_cluster_pool.clone()))
		.and_then(handler::arb_rcp_handler);
	let routes = warp::any()
		.and(health_route
			.or(eth_rpc_route)
			.or(bsc_rpc_route)
			.or(polygon_rpc_route)
			.or(avalanche_rpc_route)
			.or(cronos_rpc_route)
			.or(eth_goerli_rpc_route)
			.or(bsc_testnet_rpc_route)
			.or(polygon_mumbai_rpc_route)
			.or(avalanche_fuji_rpc_route)
			.or(cronos_testnet_rpc_route)
			.or(sol_devnet_rpc_route)
			.or(sol_mainnet_rpc_route)
			.or(op_mainnet_rpc_route)
			.or(op_sepolia_rpc_route)
			.or(arb_mainnet_rpc_route)
		)
		.with(warp::cors().allow_any_origin())
		.with(log)
		.recover(error::handle_rejection);

	warp::serve(routes).run(([0, 0, 0, 0], env.port)).await;
}
