use std::hash::{Hash, Hasher};
use std::sync::Arc;
use warp::{http::StatusCode, reject, Reply, Rejection};
use crate::{redis::{get_str, RedisPool}, Result};
use crate::error::Error::RedisError;
use crate::ethrpc::{eth_client_method, EthRpcRequest, EvmNetworkChainId};
use crate::solrpc::{sol_client_method, SolRpcRequest, SolNetworkId};
use crate::network::Chain;
use crate::redis_cluster::RedisClusterPool;

pub async fn health_handler(_redis_pool: Arc<RedisPool>, _redis_cluster_pool: Arc<RedisClusterPool>) -> Result<impl Reply> {
	Ok("")
}

macro_rules! create_evm_rpc_handler {
    ($name: tt, $chain_id: expr) => {
        pub async fn $name(request: EthRpcRequest, redis_pool: Arc<RedisPool>, redis_cluster_pool: Arc<RedisClusterPool>) -> Result<impl Reply> {
			let res = eth_client_method(&redis_cluster_pool, $chain_id, &request).await.map_err(|e | e)?;
			Ok(warp::reply::json(&res))
		}
    };
}

macro_rules! create_sol_rpc_handler {
    ($name: tt, $chain_id: expr) => {
        pub async fn $name(request: SolRpcRequest, redis_pool: Arc<RedisPool>, redis_cluster_pool: Arc<RedisClusterPool>) -> Result<impl Reply> {
			let res = sol_client_method(&redis_cluster_pool, $chain_id, &request).await.map_err(|e | e)?;
			Ok(warp::reply::json(&res))
		}
    };
}

create_evm_rpc_handler!(eth_rcp_handler, EvmNetworkChainId::Ethereum);
create_evm_rpc_handler!(op_rcp_handler, EvmNetworkChainId::Optimism);
create_evm_rpc_handler!(op_sepolia_rcp_handler, EvmNetworkChainId::OptimismSepolia);
create_evm_rpc_handler!(arb_rcp_handler, EvmNetworkChainId::Arbitrum);
create_evm_rpc_handler!(bsc_rcp_handler, EvmNetworkChainId::BinanceSmartChain);
create_evm_rpc_handler!(polygon_rcp_handler, EvmNetworkChainId::Polygon);
create_evm_rpc_handler!(avalanche_rcp_handler, EvmNetworkChainId::Avalanche);
create_evm_rpc_handler!(cronos_rcp_handler, EvmNetworkChainId::Cronos);
create_evm_rpc_handler!(eth_goerli_rcp_handler, EvmNetworkChainId::EthereumGoerli);
create_evm_rpc_handler!(bsc_testnet_rcp_handler, EvmNetworkChainId::BinanceSmartChainTestnet);
create_evm_rpc_handler!(polygon_mumbai_rcp_handler, EvmNetworkChainId::PolygonMumbai);
create_evm_rpc_handler!(avalanche_fuji_rcp_handler, EvmNetworkChainId::AvalancheFuji);
create_evm_rpc_handler!(cronos_testnet_rcp_handler, EvmNetworkChainId::CronosTestnet);
create_sol_rpc_handler!(sol_devnet_rcp_handler, SolNetworkId::Devnet);
create_sol_rpc_handler!(sol_mainnet_rcp_handler, SolNetworkId::Mainnet);


// pub async fn rpc_handler(request: EthRpcRequest, redis_pool: RedisPool) -> Result<impl Reply> {
// 	let res = eth_client_method(&redis_pool, EvmNetworkChainId::Ethereum, &request).await.map_err(|e | e)?;
// 	Ok(warp::reply::json(&res))
// }
