use std::collections::hash_map::DefaultHasher;
use strum_macros::{<PERSON><PERSON><PERSON>, EnumIter, EnumString, IntoStaticStr};
use serde::{Serialize, Deserialize};
use std::hash::{Hash, Hasher};
use std::ptr::hash;
use std::{thread, time};
use std::time::Duration;
use hyper::body::HttpBody;
use lazy_static::lazy_static;
use rand::Rng;
use serde_json::json;
use tokio::time::sleep;
use warp::{http::StatusCode, reject, Reply, Rejection, method};
use crate::{redis_cluster::{set_str, get_str, del, get_ttl, set_nx}, Result};
use crate::error::Error::{*};
use std::collections::HashMap;
use std::fmt::format;
use crate::config::{EtherscanConfig, RpcUrl};
use std::sync::RwLock;
use strum::IntoEnumIterator;
use crate::redis_cluster::RedisClusterPool;

// Global HTTP client optimized for high throughput (100 RPS)
lazy_static! {
    static ref HTTP_CLIENT: reqwest::Client = reqwest::Client::builder()
        .timeout(Duration::from_secs(15))
        .pool_max_idle_per_host(100)        // Increased from 10 to handle 100 RPS
        .pool_idle_timeout(Duration::from_secs(90))  // Longer idle timeout
        .tcp_keepalive(Duration::from_secs(60))      // Keep connections alive
        .tcp_nodelay(true)                           // Disable Nagle's algorithm for lower latency
        .http2_adaptive_window(true)                 // Adaptive HTTP/2 flow control
        .http2_max_frame_size(Some(16384))          // Conservative frame size
        .build()
        .expect("Failed to create HTTP client");
}

#[derive(EnumString, Debug, Copy, Clone, Hash, Serialize, Deserialize)]
#[strum(serialize_all = "snake_case")]
enum EthRpcMethod {
	#[serde(rename = "net_version")]
	#[strum(serialize = "net_version")]
	NetVersion,
	#[serde(rename = "eth_chainId")]
	#[strum(serialize = "eth_chainId")]
	EthChainId,
	#[serde(rename = "net_peerCount")]
	#[strum(serialize = "net_peerCount")]
	NetPeerCount,
	#[serde(rename = "net_listening")]
	#[strum(serialize = "net_listening")]
	NetListening,
	#[serde(rename = "web3_clientVersion")]
	#[strum(serialize = "web3_clientVersion")]
	Web3ClientVersion,
	#[serde(rename = "eth_protocolVersion")]
	#[strum(serialize = "eth_protocolVersion")]
	EthProtocolVersion,
	#[serde(rename = "eth_syncing")]
	#[strum(serialize = "eth_syncing")]
	EthSyncing,
	#[serde(rename = "eth_coinbase")]
	#[strum(serialize = "eth_coinbase")]
	EthCoinbase,
	#[serde(rename = "eth_mining")]
	#[strum(serialize = "eth_mining")]
	EthMining,
	#[serde(rename = "eh_hashrate")]
	#[strum(serialize = "eh_hashrate")]
	EthHashrate,
	#[serde(rename = "eth_gasPrice")]
	#[strum(serialize = "eth_gasPrice")]
	EthGasPrice,
	#[serde(rename = "eth_accounts")]
	#[strum(serialize = "eth_accounts")]
	EthAccounts,
	#[serde(rename = "eth_blockNumber")]
	#[strum(serialize = "eth_blockNumber")]
	EthBlockNumber,
	#[serde(rename = "eth_getBalance")]
	#[strum(serialize = "eth_getBalance")]
	EthGetBalance,
	#[serde(rename = "eth_getStorageAt")]
	#[strum(serialize = "eth_getStorageAt")]
	EthGetStorageAt,
	#[serde(rename = "eth_getTransactionCount")]
	#[strum(serialize = "eth_getTransactionCount")]
	EthGetTransactionCount,
	#[serde(rename = "eth_getBlockTransactionCountByHash")]
	#[strum(serialize = "eth_getBlockTransactionCountByHash")]
	EthGetTransactionCountByHash,
	#[serde(rename = "eth_getBlockTransactionCountByNumber")]
	#[strum(serialize = "eth_getBlockTransactionCountByNumber")]
	EthGetTransactionCountByNumber,
	#[serde(rename = "eth_getBlockReceipts")]
	#[strum(serialize = "eth_getBlockReceipts")]
	EthGetBlockReceipts,
	#[serde(rename = "eth_getUncleCountByBlockHash")]
	#[strum(serialize = "eth_getUncleCountByBlockHash")]
	EthGetUncleCountByBlockHash,
	#[serde(rename = "eth_getUncleCountByBlockNumber")]
	#[strum(serialize = "eth_getUncleCountByBlockNumber")]
	EthGetUncleCountByBlockNumber,
	#[serde(rename = "eth_getCode")]
	#[strum(serialize = "eth_getCode")]
	EthGetCode,
	#[serde(rename = "eth_sign")]
	#[strum(serialize = "eth_sign")]
	EthSign,
	#[serde(rename = "eth_signTransaction")]
	#[strum(serialize = "eth_signTransaction")]
	EthSignTransaction,
	#[serde(rename = "eth_sendTransaction")]
	#[strum(serialize = "eth_sendTransaction")]
	EthSendTransaction,
	#[serde(rename = "eth_sendRawTransaction")]
	#[strum(serialize = "eth_sendRawTransaction")]
	EthSendRawTransaction,
	#[serde(rename = "eth_call")]
	#[strum(serialize = "eth_call")]
	EthCall,
	#[serde(rename = "eth_estimateGas")]
	#[strum(serialize = "eth_estimateGas")]
	EthEstimateGas,
	#[serde(rename = "eth_getBlockByHash")]
	#[strum(serialize = "eth_getBlockByHash")]
	EthGetBlockByHash,
	#[serde(rename = "eth_getBlockByNumber")]
	#[strum(serialize = "eth_getBlockByNumber")]
	EthGetBlockByNumber,
	#[serde(rename = "eth_getTransactionByHash")]
	#[strum(serialize = "eth_getTransactionByHash")]
	EthGetTransactionByHash,
	#[serde(rename = "eth_getTransactionByBlockHashAndIndex")]
	#[strum(serialize = "eth_getTransactionByBlockHashAndIndex")]
	EthGetTransactionByHashAndIndex,
	#[serde(rename = "eth_getTransactionByBlockNumberAndIndex")]
	#[strum(serialize = "eth_getTransactionByBlockNumberAndIndex")]
	EthGetTransactionByBlockNumberAndIndex,
	#[serde(rename = "eth_getTransactionReceipt")]
	#[strum(serialize = "eth_getTransactionReceipt")]
	EthGetTransactionReceipt,
	#[serde(rename = "eth_getUncleByBlockHashAndIndex")]
	#[strum(serialize = "eth_getUncleByBlockHashAndIndex")]
	EthGetUncleByBlockHashAndIndex,
	#[serde(rename = "eth_getUncleByBlockNumberAndIndex")]
	#[strum(serialize = "eth_getUncleByBlockNumberAndIndex")]
	EthGetUncleByBlockNumberAndIndex,
	#[serde(rename = "eth_getCompilers")]
	#[strum(serialize = "eth_getCompilers")]
	EthGetCompilers,
	#[serde(rename = "eth_compileSolidity")]
	#[strum(serialize = "eth_compileSolidity")]
	EthCompileSolidity,
	#[serde(rename = "eth_compileLLL")]
	#[strum(serialize = "eth_compileLLL")]
	EthCompileLLL,
	#[serde(rename = "eth_compileSerpent")]
	#[strum(serialize = "eth_compileSerpent")]
	EthCompileSerpent,
	#[serde(rename = "eth_newFilter")]
	#[strum(serialize = "eth_newFilter")]
	EthNewFilter,
	#[serde(rename = "eth_newBlockFilter")]
	#[strum(serialize = "eth_newBlockFilter")]
	EthNewBlockFilter,
	#[serde(rename = "eth_newPendingTransactionFilter")]
	#[strum(serialize = "eth_newPendingTransactionFilter")]
	EthNewPendingTransactionFilter,
	#[serde(rename = "eth_uninstallFilter")]
	#[strum(serialize = "eth_uninstallFilter")]
	EthUninstallFilter,
	#[serde(rename = "eth_getFilterChanges")]
	#[strum(serialize = "eth_getFilterChanges")]
	EthGetFilterChanges,
	#[serde(rename = "eth_getFilterLogs")]
	#[strum(serialize = "eth_getFilterLogs")]
	EthGetFilterLogs,
	#[serde(rename = "eth_getLogs")]
	#[strum(serialize = "eth_getLogs")]
	EthGetLogs,
	#[serde(rename = "eth_getWork")]
	#[strum(serialize = "eth_getWork")]
	EthGetWork,
	#[serde(rename = "eth_submitWork")]
	#[strum(serialize = "eth_submitWork")]
	EthSubmitWork,
	#[serde(rename = "eth_submitHashrate")]
	#[strum(serialize = "eth_submitHashrate")]
	EthSubmitHashrate,
	#[serde(rename = "optimism_outputAtBlock")]
	#[strum(serialize = "optimism_outputAtBlock")]
	OptimismOutputAtBlock,
	#[serde(rename = "optimism_syncStatus")]
	#[strum(serialize = "optimism_syncStatus")]
	OptimismSyncStatus,
	#[serde(rename = "optimism_rollupConfig")]
	#[strum(serialize = "optimism_rollupConfig")]
	OptimismRollupConfig,
	#[serde(rename = "optimism_version")]
	#[strum(serialize = "optimism_version")]
	OptimismVersion,
}

impl EthRpcMethod {
	fn ttl(&self) -> usize {
		match *self {
			EthRpcMethod::EthBlockNumber => 15,
			EthRpcMethod::EthGetBlockReceipts => 15,
			EthRpcMethod::EthGasPrice => 60,
			EthRpcMethod::EthGetBalance => 15,
			EthRpcMethod::EthGetBlockByHash => 300, // check tag
			EthRpcMethod::EthGetBlockByNumber => 300, // check tag
			EthRpcMethod::EthGetTransactionCount => 60,
			EthRpcMethod::EthGetTransactionCountByNumber => 300,
			EthRpcMethod::EthGetTransactionCountByHash => 300,
			EthRpcMethod::EthGetTransactionByHash => 300,
			EthRpcMethod::EthGetTransactionReceipt => 300,
			EthRpcMethod::OptimismSyncStatus => 1,
			_ => 1
		}
	}
}

#[derive(Serialize, Deserialize, Debug)]
struct EthRpcResponse {
	jsonrpc: &'static str,
	id: u64,
	result: serde_json::Value,
}

#[derive(Serialize, Deserialize, Debug)]
struct UpstreamEthRpcResponse {
	result: serde_json::Value,
	error: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct EthRpcRequest {
	id: u64,
	method: EthRpcMethod,
	params: serde_json::Value,
}

impl Hash for EthRpcRequest {
	fn hash<H: Hasher>(&self, state: &mut H) {
		self.method.hash(state);
		self.params.to_string().hash(state);
	}
}

#[derive(IntoStaticStr, Debug, Deserialize, Hash, PartialEq, Eq, EnumIter, Clone, Copy)]
#[repr(u32)]
pub enum EvmNetworkChainId {
	Ethereum = 1,
	Optimism = 10,
	Cronos = 25,
	BinanceSmartChain = 56,
	Polygon = 137,
	Arbitrum = 42161,
	Avalanche = 43114,
	// Testnet
	EthereumGoerli = 5,
	BinanceSmartChainTestnet = 97,
	CronosTestnet = 338,
	AvalancheFuji = 43113,
	PolygonMumbai = 80001,
	OptimismSepolia = 11155420,
}

async fn eth_upstream_request_with_retry(url: &str, method: &EthRpcMethod, params: &serde_json::Value, max_retries: u32) -> Result<UpstreamEthRpcResponse> {
	let mut last_error = None;

	for attempt in 0..=max_retries {
		let delay_ms = if attempt == 0 { 0 } else { 50 * (1 << (attempt - 1)) }; // 0, 50, 100, 200, 400ms

		if delay_ms > 0 {
			// Add jitter to prevent thundering herd
			let jitter = rand::thread_rng().gen_range(0..delay_ms/4);
			sleep(Duration::from_millis(delay_ms + jitter)).await;
		}

		match eth_upstream_request_single(url, method, params).await {
			Ok(response) => return Ok(response),
			Err(e) => {
				last_error = Some(e);
				if attempt < max_retries {
					eprintln!("Upstream request failed (attempt {}/{}), retrying in {}ms: method: {:?}, url: {}",
						attempt + 1, max_retries + 1, delay_ms, method, url);
				}
			}
		}
	}

	eprintln!("All {} upstream request attempts failed for method: {:?}, url: {}", max_retries + 1, method, url);
	Err(last_error.unwrap_or(warp::Rejection::from(RpcUpstreamError)))
}

async fn eth_upstream_request_single(url: &str, method: &EthRpcMethod, params: &serde_json::Value) -> Result<UpstreamEthRpcResponse> {
	let resp = HTTP_CLIENT
		.post(url)
		.json(&json!({
			"jsonrpc": "2.0",
			"id": 0,
			"method": method,
			"params": params
		}))
		.send()
		.await
		.map_err(|e| {
			// Check if it's a timeout or connection error that should be retried
			if e.is_timeout() || e.is_connect() || e.is_request() {
				eprintln!("Retryable upstream error: {}, method: {:?}, params: {}", e, method, params);
			} else {
				eprintln!("Non-retryable upstream error: {}, method: {:?}, params: {}", e, method, params);
			}
			RpcUpstreamError
		})?;

	let text = resp.text().await.map_err(|e| {
		eprintln!("Error reading upstream response: {}, method: {:?}, params: {}", e, method, params);
		RpcUpstreamError
	})?;

	let payload = serde_json::from_str::<UpstreamEthRpcResponse>(&text).map_err(|e| {
		eprintln!("Error parsing upstream response: {}, url: {}, method: {:?}, params: {}, resp: {}", e, url, method, params, text);
		RpcUpstreamError
	})?;

	if payload.error != None {
		return Err(warp::Rejection::from(RpcUpstreamError));
	}

	Ok(payload)
}

// Wrapper function for backward compatibility
async fn eth_upstream_request(url: &str, method: &EthRpcMethod, params: &serde_json::Value) -> Result<UpstreamEthRpcResponse> {
	eth_upstream_request_with_retry(url, method, params, 2).await // Default 3 attempts (0 + 2 retries)
}

pub async fn eth_client_method(redis_pool: &RedisClusterPool, chain_id: EvmNetworkChainId, request: &EthRpcRequest) -> Result<impl Serialize> {
	let mut h = DefaultHasher::new();
	chain_id.hash(&mut h);
	request.hash(&mut h);
	let request_hash = h.finish().to_string();
	if let Some(cached_value) = get_str(redis_pool, &request_hash).await? {
		let res = EthRpcResponse {
			jsonrpc: "2.0",
			id: request.id,
			result: serde_json::from_str(cached_value.as_str()).unwrap()
		};
		// let cached_ttl = get_ttl(redis_pool, &request_hash).await?
		// if cached_ttl > 2 {
		//
		// }
		return Ok(res);
	}
	match request.method {
		EthRpcMethod::NetVersion | EthRpcMethod::EthChainId => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!(chain_id as u8)
			};
			Ok(res)
		}
		EthRpcMethod::NetPeerCount => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!("0x2a")
			};
			Ok(res)
		}
		EthRpcMethod::NetListening => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!(true)
			};
			Ok(res)
		}
		EthRpcMethod::Web3ClientVersion | EthRpcMethod::OptimismVersion => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!("BillyRpc/0.1")
			};
			Ok(res)
		}
		EthRpcMethod::EthProtocolVersion => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!("0x3f")
			};
			Ok(res)
		}
		EthRpcMethod::EthSyncing => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!(false),
			};
			Ok(res)
		}
		EthRpcMethod::EthCoinbase => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!("******************************************"),
			};
			Ok(res)
		}
		EthRpcMethod::EthMining => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!(true),
			};
			Ok(res)
		}
		EthRpcMethod::EthHashrate => {
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: json!("0x0"),
			};
			Ok(res)
		}
		EthRpcMethod::EthSendRawTransaction | EthRpcMethod::EthSendTransaction => {
			let etherscan_config = &get_etherscan_config(chain_id);
			if let Some(etherscan_config) = etherscan_config {
				if let Some(params0) = request.params[0].as_str() {
					let url = format!("{}?module=proxy&action=eth_sendRawTransaction&hex={}&apikey={}",
						etherscan_config.url, params0, etherscan_config.api_key);
					println!("Calling {}", url);
					let resp = HTTP_CLIENT
						.get(&url)
						.timeout(Duration::from_secs(12))
						.send()
						.await
						.map_err(|e| {
							eprintln!("error connecting to etherscan: {}", e);
							RpcUpstreamError
						})?;

					let res = match resp.json().await {
						Ok(payload) => {
							println!("Upstream res {:?}", payload);
							payload
						}
						Err(e) => {
							eprintln!("eth_sendRawTransaction: {:?}", e);
							return Err(warp::Rejection::from(RpcUpstreamError));
						}
					};

					let payload: UpstreamEthRpcResponse = serde_json::from_value(res).map_err(|e| {
						eprintln!("error connecting to etherscan: {}", e);
						RpcUpstreamError
					})?;
					if payload.error != None {
						return Err(warp::Rejection::from(RpcUpstreamError));
					}
					let res = EthRpcResponse {
						jsonrpc: "2.0",
						id: request.id,
						result: payload.result,
					};
					return Ok(res);
				} else {
					return Err(warp::Rejection::from(RpcUpstreamError));
				}
			}
			let upstream_res = eth_upstream_request(&get_upstream_rpc_url(chain_id), &request.method, &request.params).await?;
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: upstream_res.result,
			};
			Ok(res)
		}
		_ => {
			let lock_request_hash = "LOCK".to_owned() + &request_hash;
			let mut ttl = request.method.ttl();
			if ttl > 0 {
				let lock = set_nx(redis_pool, &lock_request_hash, 15).await?;
				if !lock {
					// Optimized for high RPS - exponential backoff with jitter
					let mut count = 0u32;
					let mut delay = 10u64; // Start with 10ms
					loop {
						count += 1;
						// Add jitter to prevent thundering herd at high RPS
						let jitter = rand::thread_rng().gen_range(0..delay/2);
						sleep(Duration::from_millis(delay + jitter)).await;

						if let Some(cached_value) = get_str(redis_pool, &request_hash).await? {
							let res = EthRpcResponse {
								jsonrpc: "2.0",
								id: request.id,
								result: serde_json::from_str(cached_value.as_str()).unwrap()
							};
							return Ok(res);
						}
						if count >= 3 { // Reduced to 3 retries for high RPS
							break;
						}
						delay = std::cmp::min(delay * 2, 100); // Exponential backoff, max 100ms
					}
				}
			}
			let upstream_res = eth_upstream_request(&get_upstream_rpc_url(chain_id), &request.method, &request.params).await?;
			if let Some(params0) = request.params[0].as_str() {
				if params0.eq("latest") {
					ttl = 15;
				}
			}
			if let Some(params1) = request.params[1].as_str() {
				if params1.eq("latest") {
					ttl = 15;
				}
			}
			if ttl > 0 && !upstream_res.result.is_null() {
				let s = match request.method {
					EthRpcMethod::EthGetTransactionByHash => true,
					EthRpcMethod::EthGetTransactionReceipt => true,
					_ => false
				};
				if s && upstream_res.result["blockNumber"].is_null() {
					ttl = 5;
				}
				set_str(redis_pool, &request_hash, &upstream_res.result.to_string(), ttl).await?;
				del(redis_pool, &lock_request_hash).await?;
			}
			let res = EthRpcResponse {
				jsonrpc: "2.0",
				id: request.id,
				result: upstream_res.result,
			};
			Ok(res)
			// Err(warp::Rejection::from(RpcMethodNotExist))
		}
	}
}

lazy_static! {
    pub static ref ETH_RPC_NODE_URL: RwLock<HashMap<EvmNetworkChainId, Vec<RpcUrl>>> = RwLock::new({
		let mut h = HashMap::new();
		for evm_network_chain_id in EvmNetworkChainId::iter() {
			h.insert(evm_network_chain_id, Vec::<RpcUrl>::new());
		}
		h
	});
}

lazy_static! {
    pub static ref ETHERSCAN_CONFIG: RwLock<HashMap<EvmNetworkChainId, EtherscanConfig>> = RwLock::new({
		HashMap::new()
	});
}

// Cache for URL selection to avoid repeated RwLock reads at high RPS
lazy_static! {
    static ref URL_CACHE: std::sync::RwLock<std::collections::HashMap<EvmNetworkChainId, Vec<String>>> =
        std::sync::RwLock::new(std::collections::HashMap::new());
}

fn get_upstream_rpc_url(chain_id: EvmNetworkChainId) -> String {
	// Try cache first to avoid RwLock contention at high RPS
	{
		let cache = URL_CACHE.read().unwrap();
		if let Some(urls) = cache.get(&chain_id) {
			if !urls.is_empty() {
				let rand = rand::thread_rng().gen_range(0..urls.len());
				return urls[rand].clone();
			}
		}
	}

	// Cache miss - populate cache and return URL
	let map = ETH_RPC_NODE_URL.read().unwrap();
	if let Some(rpc_urls) = map.get(&chain_id) {
		if !rpc_urls.is_empty() {
			// Extract URLs into cache
			let urls: Vec<String> = rpc_urls.iter().map(|rpc| rpc.url.clone()).collect();
			let selected_url = urls[rand::thread_rng().gen_range(0..urls.len())].clone();

			// Update cache
			{
				let mut cache = URL_CACHE.write().unwrap();
				cache.insert(chain_id, urls);
			}

			return selected_url;
		}
	}
	String::new()
}

// Function to refresh URL cache when configuration changes
pub fn refresh_url_cache() {
	let mut cache = URL_CACHE.write().unwrap();
	cache.clear();

	let map = ETH_RPC_NODE_URL.read().unwrap();
	for (chain_id, rpc_urls) in map.iter() {
		if !rpc_urls.is_empty() {
			let urls: Vec<String> = rpc_urls.iter().map(|rpc| rpc.url.clone()).collect();
			cache.insert(*chain_id, urls);
		}
	}
}

fn get_etherscan_config(chain_id: EvmNetworkChainId) -> Option<EtherscanConfig> {
	let map = ETHERSCAN_CONFIG.read().unwrap();
	map.get(&chain_id).cloned()
}