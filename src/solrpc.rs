use std::collections::hash_map::Default<PERSON>ash<PERSON>;
use strum_macros::{Display, EnumIter, EnumString, IntoStaticStr};
use serde::{Serialize, Deserialize};
use std::hash::{Hash, Hasher};
use std::ptr::hash;
use std::{thread, time};
use std::time::Duration;
use hyper::body::HttpBody;
use lazy_static::lazy_static;
use rand::Rng;
use serde_json::json;
use tokio::time::sleep;
use warp::{http::StatusCode, reject, Reply, Rejection, method};
use crate::{redis_cluster::{set_str, get_str, del, get_ttl, set_nx}, Result};
use crate::error::Error::{*};
use std::collections::HashMap;
use crate::config::RpcUrl;
use std::sync::RwLock;
use strum::IntoEnumIterator;
use crate::redis_cluster::RedisClusterPool;

// Reuse the global HTTP client from ethrpc
use crate::ethrpc::HTTP_CLIENT;

#[derive(EnumString, Debug, Copy, Clone, Hash, Serialize, Deserialize)]
#[strum(serialize_all = "camelCase")]
#[serde(rename_all = "camelCase")]
enum SolRpcMethod {
	GetAccountInfo,
	GetBalance,
	GetBlockHeight,
	GetBlock,
	GetBlockProduction,
	GetBlockCommitment,
	GetBlocks,
	GetBlocksWithLimit,
	GetBlockTime,
	GetClusterNodes,
	GetEpochInfo,
	GetEpochSchedule,
	GetFeeForMessage,
	GetFirstAvailableBlock,
	GetGenesisHash,
	GetHealth,
	GetHighestSnapshotSlot,
	GetIdentity,
	GetInflationGovernor,
	GetInflationRate,
	GetInflationReward,
	GetLargestAccounts,
	GetLatestBlockhash,
	GetLeaderSchedule,
	GetMaxRetransmitSlot,
	GetMaxShredInsertSlot,
	GetMinimumBalanceForRentExemption,
	GetMultipleAccounts,
	GetProgramAccounts,
	GetRecentPerformanceSamples,
	GetRecentPrioritizationFees,
	GetSignaturesForAddress,
	GetSignatureStatuses,
	GetSlot,
	GetSlotLeader,
	GetSlotLeaders,
	GetStakeActivation,
	GetStakeMinimumDelegation,
	GetSupply,
	GetTokenAccountBalance,
	GetTokenAccountsByDelegate,
	GetTokenAccountsByOwner,
	GetTokenLargestAccounts,
	GetTokenSupply,
	GetTransaction,
	GetTransactionCount,
	GetVersion,
	GetVoteAccounts,
	IsBlockhashValid,
	MinimumLedgerSlot,
	RequestAirdrop,
	SendTransaction,
	SimulateTransaction
}

impl SolRpcMethod {
	fn ttl(&self) -> usize {
		match *self {
			SolRpcMethod::GetBlockHeight => 5,
			SolRpcMethod::GetSlot => 5,
			SolRpcMethod::GetBlock => 300,
			SolRpcMethod::GetLatestBlockhash => 5,
			SolRpcMethod::GetTransaction => 300,
			SolRpcMethod::GetFeeForMessage => 300,
			_ => 1
		}
	}
}

#[derive(Serialize, Deserialize, Debug)]
struct SolRpcResponse {
	jsonrpc: &'static str,
	id: String,
	result: serde_json::Value,
}

#[derive(Serialize, Deserialize, Debug)]
struct UpstreamSolRpcResponse {
	result: serde_json::Value,
	error: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct SolRpcRequest {
	id: String,
	method: SolRpcMethod,
	params: serde_json::Value,
}

impl Hash for SolRpcRequest {
	fn hash<H: Hasher>(&self, state: &mut H) {
		self.method.hash(state);
		self.params.to_string().hash(state);
	}
}

#[derive(IntoStaticStr, Debug, Deserialize, Hash, PartialEq, Eq, EnumIter, Clone, Copy)]
#[repr(u32)]
pub enum SolNetworkId {
	Mainnet = 1000001,
	// Testnet
	Devnet = 1000002,
	Testnet = 1000003,
}

async fn sol_upstream_request_with_retry(url: &str, method: &SolRpcMethod, params: &serde_json::Value, max_retries: u32) -> Result<UpstreamSolRpcResponse> {
	let mut last_error = None;

	for attempt in 0..=max_retries {
		let delay_ms = if attempt == 0 { 0 } else { 50 * (1 << (attempt - 1)) }; // 0, 50, 100, 200, 400ms

		if delay_ms > 0 {
			// Add jitter to prevent thundering herd
			let jitter = rand::thread_rng().gen_range(0..delay_ms/4);
			sleep(Duration::from_millis(delay_ms + jitter)).await;
		}

		match sol_upstream_request_single(url, method, params).await {
			Ok(response) => return Ok(response),
			Err(e) => {
				last_error = Some(e);
				if attempt < max_retries {
					eprintln!("Solana upstream request failed (attempt {}/{}), retrying in {}ms: method: {:?}, url: {}",
						attempt + 1, max_retries + 1, delay_ms, method, url);
				}
			}
		}
	}

	eprintln!("All {} Solana upstream request attempts failed for method: {:?}, url: {}", max_retries + 1, method, url);
	Err(last_error.unwrap_or(warp::Rejection::from(RpcUpstreamError)))
}

async fn sol_upstream_request_single(url: &str, method: &SolRpcMethod, params: &serde_json::Value) -> Result<UpstreamSolRpcResponse> {
	let resp = HTTP_CLIENT
		.post(url)
		.json(&json!({
			"jsonrpc": "2.0",
			"id": 0,
			"method": method,
			"params": params
		}))
		.timeout(Duration::from_secs(30))
		.send()
		.await
		.map_err(|e| {
			// Check if it's a timeout or connection error that should be retried
			if e.is_timeout() || e.is_connect() || e.is_request() {
				eprintln!("Retryable Solana upstream error: {}, method: {:?}, params: {}", e, method, params);
			} else {
				eprintln!("Non-retryable Solana upstream error: {}, method: {:?}, params: {}", e, method, params);
			}
			RpcUpstreamError
		})?;

	let text = resp.text().await.map_err(|e| {
		eprintln!("Error reading Solana upstream response: {}, method: {:?}, params: {}", e, method, params);
		RpcUpstreamError
	})?;

	let payload = serde_json::from_str::<UpstreamSolRpcResponse>(&text).map_err(|e| {
		eprintln!("Error parsing Solana upstream response: {}, url: {}, method: {:?}, params: {}, resp: {}", e, url, method, params, text);
		RpcUpstreamError
	})?;

	if payload.error != None {
		return Err(warp::Rejection::from(RpcUpstreamError));
	}

	Ok(payload)
}

// Wrapper function for backward compatibility
async fn sol_upstream_request(url: &str, method: &SolRpcMethod, params: &serde_json::Value) -> Result<UpstreamSolRpcResponse> {
	sol_upstream_request_with_retry(url, method, params, 2).await // Default 3 attempts (0 + 2 retries)
}

pub async fn sol_client_method(redis_pool: &RedisClusterPool, chain_id: SolNetworkId, request: &SolRpcRequest) -> Result<impl Serialize> {
	let mut h = DefaultHasher::new();
	chain_id.hash(&mut h);
	request.hash(&mut h);
	let request_hash = h.finish().to_string();
	if let Some(cached_value) = get_str(redis_pool, &request_hash).await? {
		let res = SolRpcResponse {
			jsonrpc: "2.0",
			id: request.id.to_string(),
			result: serde_json::from_str(cached_value.as_str()).unwrap()
		};
		return Ok(res);
	}
	match request.method {
		_ => {
			let lock_request_hash = "LOCK".to_owned() + &request_hash;
			let mut ttl = request.method.ttl();
			if ttl > 0 {
				let lock = set_nx(redis_pool, &lock_request_hash, 15).await?;
				if !lock {
					let mut count = 0u32;
					loop {
						count += 1;
						sleep(Duration::from_millis(100)).await;
						if let Some(cached_value) = get_str(redis_pool, &request_hash).await? {
							let res = SolRpcResponse {
								jsonrpc: "2.0",
								id: request.id.to_string(),
								result: serde_json::from_str(cached_value.as_str()).unwrap()
							};
							return Ok(res);
						}
						if count >= 100 {
							break;
						}
					}
				}
			}
			let upstream_res = sol_upstream_request(&get_upstream_rpc_url(chain_id), &request.method, &request.params).await?;
			if ttl > 0 && !upstream_res.result.is_null() {
				set_str(redis_pool, &request_hash, &upstream_res.result.to_string(), ttl).await?;
				del(redis_pool, &lock_request_hash).await?;
			}
			let res = SolRpcResponse {
				jsonrpc: "2.0",
				id: request.id.to_string(),
				result: upstream_res.result,
			};
			Ok(res)
			// Err(warp::Rejection::from(RpcMethodNotExist))
		}
	}
}

lazy_static! {
    pub static ref SOL_RPC_NODE_URL: RwLock<HashMap<SolNetworkId, Vec<RpcUrl>>> = RwLock::new({
		let mut h = HashMap::new();
		for network_id in SolNetworkId::iter() {
			h.insert(network_id, Vec::<RpcUrl>::new());
		}
		h
	});
}

fn get_upstream_rpc_url(chain_id: SolNetworkId) -> String {
	let rand = rand::thread_rng().gen_range(0..100);
	let map = SOL_RPC_NODE_URL.read().unwrap();
	let len = map.get(&chain_id).unwrap().len();
	map.get(&chain_id).unwrap().get(rand % len).unwrap().url.to_string()
}
