image: alpine:latest

variables:
  CONTAINER_BUILDER_IMAGE: ${CI_REGISTRY}/${CI_PROJECT_PATH}:builder_${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHA}
  CONTAINER_BUILDER_IMAGE_LATEST: ${CI_REGISTRY}/${CI_PROJECT_PATH}:builder_latest
  CONTAINER_IMAGE: ${CI_REGISTRY}/${CI_PROJECT_PATH}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHA}
  CONTAINER_IMAGE_LATEST: ${CI_REGISTRY}/${CI_PROJECT_PATH}:latest
  RELEASE_VERSION: ${CI_COMMIT_SHORT_SHA}

stages:
  - release
  - deploy

release:
  image: docker:20
  services:
    - docker:20-dind
  stage: release
  before_script:
    - export RELEASE_VERSION="${CI_COMMIT_SHORT_SHA}"
  script:
    - docker login -u gitlab-ci-token -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - docker pull ${CONTAINER_BUILDER_IMAGE_LATEST} || true
    - docker pull ${CONTAINER_IMAGE_LATEST} || true
    - docker build --target builder --cache-from ${CONTAINER_BUILDER_IMAGE_LATEST} -t ${CONTAINER_BUILDER_IMAGE} .
    - docker tag ${CONTAINER_BUILDER_IMAGE} ${CONTAINER_BUILDER_IMAGE_LATEST}
    - docker push ${CONTAINER_BUILDER_IMAGE}
    - docker push ${CONTAINER_BUILDER_IMAGE_LATEST}
    - docker build --cache-from ${CONTAINER_BUILDER_IMAGE_LATEST} --cache-from ${CONTAINER_IMAGE_LATEST} -t ${CONTAINER_IMAGE} .
    - docker tag ${CONTAINER_IMAGE} ${CONTAINER_IMAGE_LATEST}
    - docker push ${CONTAINER_IMAGE}
    - docker push ${CONTAINER_IMAGE_LATEST}

deploy-to-bullb-stage:
  image: docker:18.09
  environment: production
  services:
    - docker:18.09.0-dind
  needs: [release]
  stage: deploy
  variables:
    BULLB_REGISTRY: registry.bull-b.com
    BULLB_PROJECT_PATH: root/crypto-wallet-backend/rpc-proxy/stage
    BULLB_CONTAINER_IMAGE: ${BULLB_REGISTRY}/${BULLB_PROJECT_PATH}:${CI_JOB_ID}
  script:
    - docker login -u gitlab-ci-token -p ${CI_JOB_TOKEN} ${CI_REGISTRY}
    - docker pull ${CONTAINER_IMAGE}
    - docker login -u root -p sMmrL_EQej2-C7oyZXL6 registry.bull-b.com
    - docker tag ${CONTAINER_IMAGE} ${BULLB_CONTAINER_IMAGE}
    - docker push ${BULLB_CONTAINER_IMAGE}
  when: manual